#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Test script to test the 17-column query directly with the database connector
"""

import sys
import os
import json

# Add common path to import db_utils
sys.path.append(os.path.join(os.path.dirname(__file__), 'common'))

from db_utils import FirebirdConnector

def test_17_column_query():
    """Test the 17-column query directly"""
    
    # Load configuration
    config_file = "client_config_oauth_tokens.json"
    if os.path.exists(config_file):
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
    else:
        print(f"Configuration file not found: {config_file}")
        return
    
    # Get database configuration
    db_config = config.get('database', {})
    
    # Create connector
    connector = FirebirdConnector(
        db_path=db_config.get('path'),
        username=db_config.get('username', 'sysdba'),
        password=db_config.get('password', 'masterkey'),
        isql_path=db_config.get('isql_path'),
        use_localhost=db_config.get('use_localhost', True)
    )
    
    # The 17-column query
    query = """
SELECT 
  o.EMPID AS KaryawanID,
  o.ID AS OvertimeID,
  o.INPDATE AS TanggalOvertime,
  o.HOURS AS JamKerja,
  SUBSTRING(f.FIELDNO FROM 1 FOR 6) AS KodeField,
  (j.EXPTYPE || j.ITEMNO || j.SUBNO) AS AccCode,
  j.DESCRIPTION AS DeskripsiPekerjaan,
  o.BASICRATE AS TarifDasar,
  o.ADDRATE AS TarifTambahan,
  o.HOURS * o.BASICRATE AS NilaiDasar,
  o.HOURS * o.ADDRATE AS NilaiTambahan,
  o.REMARKS AS Catatan,
  SUBSTRING(f.FIELDNO FROM 1 FOR 2) AS KodeField_2Char,
  SUBSTRING(j.EXPTYPE FROM 1 FOR 2) AS AccCode_2Char,
  a.VEHNO AS NomorKendaraan,
  a.MODEL AS ModelKendaraan,
  o.VEHID AS VehicleID
FROM OVERTIME o
JOIN JOBCODE j ON o.JOBID = j.ID
JOIN OCFIELD f ON o.FIELDID = f.ID
LEFT JOIN VEHCODE a ON o.VEHID = a.ID
WHERE o.INPDATE BETWEEN '2025-03-01' AND '2025-03-31'
  AND SUBSTRING(j.EXPTYPE FROM 1 FOR 2) = 'PT'
  AND a.ID IS NULL
ORDER BY o.EMPID, o.INPDATE
"""
    
    print("Testing 17-column query...")
    print("="*80)
    print(f"Query: {query[:200]}...")
    print("="*80)
    
    try:
        # Execute query
        result = connector.execute_query(query)
        
        # Analyze results
        print(f"Number of result sets: {len(result)}")
        
        for i, result_set in enumerate(result):
            headers = result_set.get('headers', [])
            rows = result_set.get('rows', [])
            row_count = result_set.get('row_count', len(rows))
            column_count = result_set.get('column_count', len(headers))
            
            print(f"\nResult set {i+1}:")
            print(f"  Headers ({len(headers)}): {headers}")
            print(f"  Row count: {row_count}")
            print(f"  Column count: {column_count}")
            print(f"  Actual headers length: {len(headers)}")
            
            if rows:
                print(f"  First row keys: {list(rows[0].keys()) if isinstance(rows[0], dict) else 'Not a dict'}")
                print(f"  First row values: {list(rows[0].values()) if isinstance(rows[0], dict) else rows[0]}")
                
                # Check if all expected columns are present
                expected_columns = [
                    'KaryawanID', 'OvertimeID', 'TanggalOvertime', 'JamKerja',
                    'KodeField', 'AccCode', 'DeskripsiPekerjaan', 'TarifDasar',
                    'TarifTambahan', 'NilaiDasar', 'NilaiTambahan', 'Catatan',
                    'KodeField_2Char', 'AccCode_2Char', 'NomorKendaraan',
                    'ModelKendaraan', 'VehicleID'
                ]
                
                print(f"\nExpected columns ({len(expected_columns)}): {expected_columns}")
                
                missing_columns = [col for col in expected_columns if col not in headers]
                extra_columns = [col for col in headers if col not in expected_columns]
                
                if missing_columns:
                    print(f"Missing columns: {missing_columns}")
                if extra_columns:
                    print(f"Extra columns: {extra_columns}")
                    
                if len(headers) == len(expected_columns) and not missing_columns:
                    print("✅ All 17 columns are present!")
                else:
                    print(f"❌ Column mismatch! Expected 17, got {len(headers)}")
                    
            else:
                print("  No data rows returned")
        
    except Exception as e:
        print(f"Error executing query: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_17_column_query() 